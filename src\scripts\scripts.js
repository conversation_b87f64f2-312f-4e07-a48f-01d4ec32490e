 // Intersection Observer for scroll-triggered animations
  const observerOptions = {
    threshold: 0.1, // Trigger when 10% of element is visible
    rootMargin: '0px 0px -50px 0px' // Trigger slightly before element comes into view
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        const delay = parseFloat(element.dataset.delay || '0') * 1000;

        setTimeout(() => {
          element.classList.add('animate');
        }, delay);

        // Stop observing this element once animated
        observer.unobserve(element);
      }
    });
  }, observerOptions);

  // Observe all scroll-fade-in elements
  document.addEventListener('DOMContentLoaded', () => {
    const fadeElements = document.querySelectorAll('.scroll-fade-in');
    fadeElements.forEach(element => {
      observer.observe(element);
    });
  });